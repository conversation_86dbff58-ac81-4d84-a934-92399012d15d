--- Log Start: 2025-08-05 15:01:06 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3038986.73it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3201177.56it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3347233.71it/s]
🔧 加载PLM和tokenizer...
❌ 初始化模型组件失败: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 476, in cached_files
    hf_hub_download(
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 708, in _get_config_dict
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 318, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 529, in cached_files
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision, repo_type)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 144, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/predict.py", line 114, in _initialize_model
    self.plm, self.tokenizer, self.model_config, self.WrapperClass = load_plm_from_config(
                                                                     ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/utils.py", line 132, in load_plm_from_config
    model_config = BertConfig.from_pretrained(model_path)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 609, in from_pretrained
    config_dict, kwargs = cls.get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 649, in get_config_dict
    config_dict, kwargs = cls._get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 731, in _get_config_dict
    raise OSError(
OSError: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
❌ 分类器初始化失败: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
--- Log Start: 2025-08-05 15:14:41 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3203855.81it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3758936.02it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3710345.85it/s]
🔧 加载PLM和tokenizer...
❌ 初始化模型组件失败: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 476, in cached_files
    hf_hub_download(
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 708, in _get_config_dict
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 318, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 529, in cached_files
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision, repo_type)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 144, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/predict.py", line 114, in _initialize_model
    self.plm, self.tokenizer, self.model_config, self.WrapperClass = load_plm_from_config(
                                                                     ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/utils.py", line 132, in load_plm_from_config
    model_config = BertConfig.from_pretrained(model_path)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 609, in from_pretrained
    config_dict, kwargs = cls.get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 649, in get_config_dict
    config_dict, kwargs = cls._get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 731, in _get_config_dict
    raise OSError(
OSError: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
❌ 分类器初始化失败: Can't load the configuration of '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese' is the correct path to a directory containing a config.json file
--- Log Start: 2025-08-05 15:15:55 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3224850.10it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3690529.54it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3635063.47it/s]
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 556.79it/s]
start convert_features_to_dataset
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:129: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 528.88it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 498.22it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 505.79it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 515.19it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 417.76it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 496.25it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 506.92it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 513.53it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 553.72it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 580.67it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 556.39it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 570.72it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 7it [00:00, 552.27it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 73.64%
   - 完全匹配题目: 281
   - 部分匹配题目: 64
   - 无匹配题目: 78

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 155题，占比 82.45%
   - 未命中任何知识点: 33题，占比 17.55%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 11题，占比 8.53%
   - 命中 2 个知识点: 93题，占比 72.09%
   - 未命中任何知识点: 25题，占比 19.38%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 7题，占比 11.86%
   - 命中 2 个知识点: 10题，占比 16.95%
   - 命中 3 个知识点: 33题，占比 55.93%
   - 未命中任何知识点: 9题，占比 15.25%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 3题，占比 23.08%
   - 命中 2 个知识点: 1题，占比 7.69%
   - 命中 3 个知识点: 8题，占比 61.54%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 7.69%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 3题，占比 25.00%
   - 命中 2 个知识点: 1题，占比 8.33%
   - 命中 3 个知识点: 5题，占比 41.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 3题，占比 25.00%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 2题，占比 22.22%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 5题，占比 55.56%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 2题，占比 22.22%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 1题，占比 20.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 4题，占比 80.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 1题，占比 33.33%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 33.33%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 3题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-05 15:18:37 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3248069.02it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3819107.31it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3736137.72it/s]
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 552.80it/s]
start convert_features_to_dataset
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:129: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 525.13it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 510.28it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 423.90it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 550.38it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 553.44it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 402.40it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 562.93it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 511.32it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 567.29it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 580.76it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 559.72it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 573.01it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 7it [00:00, 424.77it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 90.89%
   - 完全匹配题目: 357
   - 部分匹配题目: 54
   - 无匹配题目: 12

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 181题，占比 96.28%
   - 未命中任何知识点: 7题，占比 3.72%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 6题，占比 4.65%
   - 命中 2 个知识点: 121题，占比 93.80%
   - 未命中任何知识点: 2题，占比 1.55%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 1题，占比 1.69%
   - 命中 2 个知识点: 2题，占比 3.39%
   - 命中 3 个知识点: 55题，占比 93.22%
   - 未命中任何知识点: 1题，占比 1.69%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 2题，占比 15.38%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 10题，占比 76.92%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 7.69%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 11题，占比 91.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 8.33%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 2题，占比 22.22%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 7题，占比 77.78%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 5题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 33.33%
   - 命中 3 个知识点: 2题，占比 66.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 1题，占比 33.33%
   - 命中 2 个知识点: 1题，占比 33.33%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-08-05 16:00:38 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3194403.05it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3794099.13it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3710345.85it/s]
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 534.88it/s]
start convert_features_to_dataset
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:129: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 524.20it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 505.91it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 522.42it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 556.57it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 533.18it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 519.34it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 566.38it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 528.71it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 563.78it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 592.63it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 556.55it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 567.60it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 7it [00:00, 553.19it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 89.75%
   - 完全匹配题目: 350
   - 部分匹配题目: 61
   - 无匹配题目: 12

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 184题，占比 97.87%
   - 未命中任何知识点: 4题，占比 2.13%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 8题，占比 6.20%
   - 命中 2 个知识点: 115题，占比 89.15%
   - 未命中任何知识点: 6题，占比 4.65%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 5题，占比 8.47%
   - 命中 2 个知识点: 2题，占比 3.39%
   - 命中 3 个知识点: 51题，占比 86.44%
   - 未命中任何知识点: 1题，占比 1.69%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 2题，占比 15.38%
   - 命中 2 个知识点: 1题，占比 7.69%
   - 命中 3 个知识点: 10题，占比 76.92%
   - 命中 4 个知识点: 0题，占比 0.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 1题，占比 8.33%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 10题，占比 83.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 8.33%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 2题，占比 22.22%
   - 命中 2 个知识点: 1题，占比 11.11%
   - 命中 3 个知识点: 6题，占比 66.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 5题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 2题，占比 66.67%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 1题，占比 33.33%
   - 命中 2 个知识点: 1题，占比 33.33%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
--- Log Start: 2025-08-05 16:03:10 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 16 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_16.json
------------ length few-shot: 3872 ------------
length dataset['train']: 3872

  0%|          | 0/3872 [00:00<?, ?it/s]
100%|██████████| 3872/3872 [00:00<00:00, 3226772.32it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3758936.02it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 3692140.03it/s]
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 538.49it/s]
start convert_features_to_dataset
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:129: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 522.45it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 507.10it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 511.35it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 546.87it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 534.12it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 505.30it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 569.26it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 515.86it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 568.02it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 592.51it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 545.11it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 569.72it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 7it [00:00, 534.08it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 89.75%
   - 完全匹配题目: 350
   - 部分匹配题目: 61
   - 无匹配题目: 12

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 184题，占比 97.87%
   - 未命中任何知识点: 4题，占比 2.13%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 8题，占比 6.20%
   - 命中 2 个知识点: 115题，占比 89.15%
   - 未命中任何知识点: 6题，占比 4.65%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 5题，占比 8.47%
   - 命中 2 个知识点: 2题，占比 3.39%
   - 命中 3 个知识点: 51题，占比 86.44%
   - 未命中任何知识点: 1题，占比 1.69%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 2题，占比 15.38%
   - 命中 2 个知识点: 1题，占比 7.69%
   - 命中 3 个知识点: 10题，占比 76.92%
   - 命中 4 个知识点: 0题，占比 0.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 1题，占比 8.33%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 10题，占比 83.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 8.33%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 2题，占比 22.22%
   - 命中 2 个知识点: 1题，占比 11.11%
   - 命中 3 个知识点: 6题，占比 66.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 5题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 2题，占比 66.67%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 1题，占比 33.33%
   - 命中 2 个知识点: 1题，占比 33.33%
   - 命中 3 个知识点: 1题，占比 33.33%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-3.json

🏁 测试完成！
--- Log Start: 2025-08-05 16:05:59 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5924 ------------
length dataset['train']: 5924

  0%|          | 0/5924 [00:00<?, ?it/s]
100%|██████████| 5924/5924 [00:00<00:00, 3068295.49it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3554681.83it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
 94%|█████████▎| 839/897 [00:00<00:00, 4811.32it/s]
100%|██████████| 897/897 [00:00<00:00, 5137.00it/s]
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 558.82it/s]
start convert_features_to_dataset
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:129: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 530.78it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 515.31it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 527.37it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 567.41it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 556.80it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 513.71it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 573.49it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 532.18it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 573.23it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 596.76it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 565.81it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 32it [00:00, 576.19it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

tokenizing: 0it [00:00, ?it/s]
tokenizing: 7it [00:00, 552.33it/s]
start convert_features_to_dataset
/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 99.52%
   - 完全匹配题目: 418
   - 部分匹配题目: 4
   - 无匹配题目: 1

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 188题，占比 100.00%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 1题，占比 0.78%
   - 命中 2 个知识点: 127题，占比 98.45%
   - 未命中任何知识点: 1题，占比 0.78%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 59题，占比 100.00%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 13题，占比 100.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 12题，占比 100.00%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 9题，占比 100.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 5题，占比 100.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 2题，占比 100.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 1题，占比 33.33%
   - 命中 9 个知识点: 2题，占比 66.67%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 1题，占比 33.33%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 1题，占比 33.33%
   - 命中 10 个知识点: 1题，占比 33.33%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-4.json

🏁 测试完成！
