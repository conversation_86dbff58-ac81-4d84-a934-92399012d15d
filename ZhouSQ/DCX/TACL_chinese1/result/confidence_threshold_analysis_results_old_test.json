[{"confidence_threshold": 0.0, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.01, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.02, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.03, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.04, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.05, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.06, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.07, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.08, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.09, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.1, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.11, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.12, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.13, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.14, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.15, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.16, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.17, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.18, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.19, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.2, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.21, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.22, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.23, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.24, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.25, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.26, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.27, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.28, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.29, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.3, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.31, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.32, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.33, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.34, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.35, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.36, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.37, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.38, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.39, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.4, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.41, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.42, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.43, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.44, "precision": 0.5555555555555556, "recall": 0.7859531772575251, "f1": 0.6509695290858726, "true_positives": 705, "false_positives": 564, "false_negatives": 192}, {"confidence_threshold": 0.45, "precision": 0.5564325177584846, "recall": 0.7859531772575251, "f1": 0.6515711645101663, "true_positives": 705, "false_positives": 562, "false_negatives": 192}, {"confidence_threshold": 0.46, "precision": 0.557753164556962, "recall": 0.7859531772575251, "f1": 0.6524757056918093, "true_positives": 705, "false_positives": 559, "false_negatives": 192}, {"confidence_threshold": 0.47, "precision": 0.5581947743467933, "recall": 0.7859531772575251, "f1": 0.6527777777777778, "true_positives": 705, "false_positives": 558, "false_negatives": 192}, {"confidence_threshold": 0.48, "precision": 0.5590800951625694, "recall": 0.7859531772575251, "f1": 0.6533827618164968, "true_positives": 705, "false_positives": 556, "false_negatives": 192}, {"confidence_threshold": 0.49, "precision": 0.5599682287529786, "recall": 0.7859531772575251, "f1": 0.6539888682745827, "true_positives": 705, "false_positives": 554, "false_negatives": 192}, {"confidence_threshold": 0.5, "precision": 0.5635491606714629, "recall": 0.7859531772575251, "f1": 0.6564245810055866, "true_positives": 705, "false_positives": 546, "false_negatives": 192}, {"confidence_threshold": 0.51, "precision": 0.5667202572347267, "recall": 0.7859531772575251, "f1": 0.6585707613264831, "true_positives": 705, "false_positives": 539, "false_negatives": 192}, {"confidence_threshold": 0.52, "precision": 0.5778688524590164, "recall": 0.7859531772575251, "f1": 0.6660368445914029, "true_positives": 705, "false_positives": 515, "false_negatives": 192}, {"confidence_threshold": 0.53, "precision": 0.5797697368421053, "recall": 0.7859531772575251, "f1": 0.6672976810222433, "true_positives": 705, "false_positives": 511, "false_negatives": 192}, {"confidence_threshold": 0.54, "precision": 0.5889724310776943, "recall": 0.7859531772575251, "f1": 0.6733524355300861, "true_positives": 705, "false_positives": 492, "false_negatives": 192}, {"confidence_threshold": 0.55, "precision": 0.6025751072961374, "recall": 0.782608695652174, "f1": 0.6808923375363725, "true_positives": 702, "false_positives": 463, "false_negatives": 195}, {"confidence_threshold": 0.56, "precision": 0.6147110332749562, "recall": 0.782608695652174, "f1": 0.6885728298185385, "true_positives": 702, "false_positives": 440, "false_negatives": 195}, {"confidence_threshold": 0.57, "precision": 0.6271338724168913, "recall": 0.778149386845039, "f1": 0.6945273631840796, "true_positives": 698, "false_positives": 415, "false_negatives": 199}, {"confidence_threshold": 0.58, "precision": 0.6439252336448598, "recall": 0.7681159420289855, "f1": 0.7005592272496187, "true_positives": 689, "false_positives": 381, "false_negatives": 208}, {"confidence_threshold": 0.59, "precision": 0.6618357487922706, "recall": 0.7636566332218506, "f1": 0.7091097308488613, "true_positives": 685, "false_positives": 350, "false_negatives": 212}, {"confidence_threshold": 0.6, "precision": 0.6855409504550051, "recall": 0.7558528428093646, "f1": 0.71898197242842, "true_positives": 678, "false_positives": 311, "false_negatives": 219}, {"confidence_threshold": 0.61, "precision": 0.6948257655755016, "recall": 0.7335562987736901, "f1": 0.7136659436008677, "true_positives": 658, "false_positives": 289, "false_negatives": 239}, {"confidence_threshold": 0.62, "precision": 0.7161862527716186, "recall": 0.7201783723522854, "f1": 0.718176764869372, "true_positives": 646, "false_positives": 256, "false_negatives": 251}, {"confidence_threshold": 0.63, "precision": 0.7336448598130841, "recall": 0.7001114827201784, "f1": 0.7164860239589277, "true_positives": 628, "false_positives": 228, "false_negatives": 269}, {"confidence_threshold": 0.64, "precision": 0.7396226415094339, "recall": 0.6555183946488294, "f1": 0.6950354609929078, "true_positives": 588, "false_positives": 207, "false_negatives": 309}, {"confidence_threshold": 0.65, "precision": 0.7506666666666667, "recall": 0.6276477146042363, "f1": 0.6836672738312083, "true_positives": 563, "false_positives": 187, "false_negatives": 334}, {"confidence_threshold": 0.66, "precision": 0.7528089887640449, "recall": 0.5975473801560758, "f1": 0.6662523306401491, "true_positives": 536, "false_positives": 176, "false_negatives": 361}, {"confidence_threshold": 0.67, "precision": 0.7527216174183515, "recall": 0.5395763656633222, "f1": 0.6285714285714286, "true_positives": 484, "false_positives": 159, "false_negatives": 413}, {"confidence_threshold": 0.68, "precision": 0.7697478991596639, "recall": 0.5105908584169454, "f1": 0.613941018766756, "true_positives": 458, "false_positives": 137, "false_negatives": 439}, {"confidence_threshold": 0.69, "precision": 0.7803571428571429, "recall": 0.48717948717948717, "f1": 0.5998627316403569, "true_positives": 437, "false_positives": 123, "false_negatives": 460}, {"confidence_threshold": 0.7, "precision": 0.7675350701402806, "recall": 0.4269788182831661, "f1": 0.5487106017191977, "true_positives": 383, "false_positives": 116, "false_negatives": 514}, {"confidence_threshold": 0.71, "precision": 0.7692307692307693, "recall": 0.39018952062430323, "f1": 0.5177514792899408, "true_positives": 350, "false_positives": 105, "false_negatives": 547}, {"confidence_threshold": 0.72, "precision": 0.7780548628428927, "recall": 0.34782608695652173, "f1": 0.4807395993836671, "true_positives": 312, "false_positives": 89, "false_negatives": 585}, {"confidence_threshold": 0.73, "precision": 0.7855072463768116, "recall": 0.30211817168338906, "f1": 0.43639291465378416, "true_positives": 271, "false_positives": 74, "false_negatives": 626}, {"confidence_threshold": 0.74, "precision": 0.7876712328767124, "recall": 0.2564102564102564, "f1": 0.3868797308662742, "true_positives": 230, "false_positives": 62, "false_negatives": 667}, {"confidence_threshold": 0.75, "precision": 0.7689075630252101, "recall": 0.2040133779264214, "f1": 0.3224669603524229, "true_positives": 183, "false_positives": 55, "false_negatives": 714}, {"confidence_threshold": 0.76, "precision": 0.7616580310880829, "recall": 0.16387959866220736, "f1": 0.26972477064220185, "true_positives": 147, "false_positives": 46, "false_negatives": 750}, {"confidence_threshold": 0.77, "precision": 0.7397260273972602, "recall": 0.12040133779264214, "f1": 0.20709491850431447, "true_positives": 108, "false_positives": 38, "false_negatives": 789}, {"confidence_threshold": 0.78, "precision": 0.7627118644067796, "recall": 0.10033444816053512, "f1": 0.1773399014778325, "true_positives": 90, "false_positives": 28, "false_negatives": 807}, {"confidence_threshold": 0.79, "precision": 0.7840909090909091, "recall": 0.07692307692307693, "f1": 0.14010152284263958, "true_positives": 69, "false_positives": 19, "false_negatives": 828}, {"confidence_threshold": 0.8, "precision": 0.7916666666666666, "recall": 0.042363433667781496, "f1": 0.08042328042328042, "true_positives": 38, "false_positives": 10, "false_negatives": 859}, {"confidence_threshold": 0.81, "precision": 0.7619047619047619, "recall": 0.017837235228539576, "f1": 0.034858387799564274, "true_positives": 16, "false_positives": 5, "false_negatives": 881}, {"confidence_threshold": 0.82, "precision": 1.0, "recall": 0.008918617614269788, "f1": 0.017679558011049725, "true_positives": 8, "false_positives": 0, "false_negatives": 889}, {"confidence_threshold": 0.83, "precision": 1.0, "recall": 0.006688963210702341, "f1": 0.013289036544850499, "true_positives": 6, "false_positives": 0, "false_negatives": 891}, {"confidence_threshold": 0.84, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.85, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.86, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.87, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.88, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.89, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.9, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.91, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.92, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.93, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.94, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.95, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.96, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.97, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.98, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.99, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.0, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.01, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.02, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.03, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.04, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}]