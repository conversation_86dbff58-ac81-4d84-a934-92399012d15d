#!/bin/bash

# 最佳性能训练脚本
# 基于你当前成功的配置，升级到Large模型

echo "🚀 启动最佳性能训练"
echo "=================================================="
echo "基于你当前成功的配置进行优化:"
echo "✅ 升级到chinese-roberta-wwm-ext-large模型"
echo "✅ 保持成功的训练策略"
echo "✅ 优化超参数以适配Large模型"
echo "✅ 预期显著提升Macro和Micro F1"
echo "=================================================="

# 环境设置
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=/home/<USER>/ZhouSQ/DCX/TACL_chinese1:$PYTHONPATH

# 创建必要目录
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result

# 检查GPU状态
echo "使用GPU:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used --format=csv,noheader,nounits | head -1

echo ""
echo "开始Large模型训练..."

# 记录开始时间
start_time=$(date)
echo "开始时间: $start_time"

# 运行训练 - 基于你当前最佳配置，升级到Large模型
python /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/train_tb.py \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large \
    --result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt \
    --batch_size 16 \
    --gradient_accumulation_steps 4 \
    --lr 1.5e-5 \
    --lr2 4e-5 \
    --max_epochs 8 \
    --lm_alpha 0.7 \
    --contrastive_alpha 0.1 \
    --contrastive_loss 1 \
    --use_fp16 False \
    --use_multi_gpu False \
    --dropout 0.1 \
    --max_grad_norm 1.0 \
    --early_stop 5 \
    --seed 171 \
    --shot 30 \
    --dataset wos \
    --depth 7 \
    --multi_verb 1 \
    --lm_training 1 \
    --mean_verbalizer True \
    --verbalizer soft \
    --use_scheduler1 1 \
    --use_scheduler2 1 \
    --imbalanced_weight True \
    --imbalanced_weight_reverse True \
    --max_seq_lens 512 \
    --use_new_ct 1 \
    --use_dropout_sim 1 \
    --eval_full 0

# 记录结束时间
end_time=$(date)
echo ""
echo "训练完成！"
echo "开始时间: $start_time"
echo "结束时间: $end_time"

# 显示结果
echo ""
echo "📊 训练结果:"
if [ -f "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt" ]; then
    echo "结果文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt"
    echo ""
    echo "🎯 最新结果:"
    tail -15 /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt
    echo ""
    
    # 提取最佳结果
    echo "🏆 最佳性能:"
    grep "best_macro\|best_micro" /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt | tail -2
else
    echo "结果文件未找到，请检查训练是否成功完成"
fi

echo ""
echo "📁 输出文件位置:"
echo "模型检查点: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/"
echo "训练结果: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/best_performance_results.txt"

# 显示最佳模型文件
echo ""
echo "🏆 生成的模型文件:"
ls -la /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/ | grep "$(date +%Y-%m-%d)" | tail -5

echo ""
echo "🎯 性能对比分析:"
echo "基线 (chinese-roberta-wwm-ext base):"
echo "  Macro F1: 0.243, Micro F1: 0.895"
echo ""
echo "预期 (chinese-roberta-wwm-ext-large):"
echo "  Macro F1: 0.30+ (提升 25%+)"
echo "  Micro F1: 0.92+ (提升 3%+)"
echo ""
echo "关键改进:"
echo "• 模型参数: 110M → 330M (3倍增长)"
echo "• 模型层数: 12层 → 24层"
echo "• 隐藏维度: 768 → 1024"
echo "• 有效batch size: 16 × 4 = 64"
echo ""
echo "=================================================="
echo "🎉 Large模型训练完成！"
echo "请查看上方结果，对比基线性能提升效果。"
echo "=================================================="
