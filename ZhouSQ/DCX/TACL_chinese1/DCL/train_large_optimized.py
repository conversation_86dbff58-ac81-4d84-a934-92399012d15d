#!/usr/bin/env python3
"""
高性能训练脚本 - 针对chinese-roberta-wwm-ext-large模型优化
充分利用4张A100 GPU，实现最佳性能
"""

import os
import sys
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
import argparse
import logging
from datetime import datetime
from tqdm import tqdm
import json
import pickle
import random
import numpy as np
from transformers import (
    AutoTokenizer, 
    AutoModel, 
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup
)
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径
sys.path.append('/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL')
from processor import *
from models.hierVerb import HierarchicalPromptModel
from util.utils import print_info, set_seed

# 设置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def setup_distributed():
    """设置分布式训练环境"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ["RANK"])
        world_size = int(os.environ['WORLD_SIZE'])
        gpu = int(os.environ['LOCAL_RANK'])
    else:
        print('Not using distributed mode')
        return False, 0, 1, 0
    
    torch.cuda.set_device(gpu)
    dist.init_process_group(backend='nccl', init_method='env://', world_size=world_size, rank=rank)
    dist.barrier()
    return True, rank, world_size, gpu

def get_optimized_args():
    """获取优化的训练参数"""
    parser = argparse.ArgumentParser("高性能Large模型训练")
    
    # 模型配置
    parser.add_argument("--model", type=str, default='bert')
    parser.add_argument("--model_name_or_path", 
                       default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large')
    parser.add_argument("--result_file", type=str, 
                       default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt")
    
    # 任务配置
    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--depth", default=7, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    
    # 损失函数配置
    parser.add_argument("--lm_training", default=1, type=int)
    parser.add_argument("--lm_alpha", default=0.6, type=float)  # 调整权重
    parser.add_argument("--constraint_loss", default=0, type=int)
    parser.add_argument("--constraint_alpha", default=0.9, type=float)
    parser.add_argument("--contrastive_loss", default=1, type=int)  # 启用对比损失
    parser.add_argument("--contrastive_alpha", default=0.1, type=float)
    parser.add_argument("--contrastive_level", default=2, type=int)
    
    # 优化的训练超参数
    parser.add_argument("--lr", default=1e-5, type=float)  # Large模型使用更小学习率
    parser.add_argument("--lr2", default=3e-5, type=float)
    parser.add_argument("--batch_size", default=16, type=int)  # 每GPU batch size
    parser.add_argument("--gradient_accumulation_steps", default=4, type=int)  # 有效batch size = 16*4*4 = 256
    parser.add_argument("--max_epochs", default=15, type=int)
    parser.add_argument("--warmup_ratio", default=0.1, type=float)
    parser.add_argument("--weight_decay", default=0.01, type=float)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    
    # 高级训练技术
    parser.add_argument("--use_fp16", default=True, type=bool)
    parser.add_argument("--scheduler_type", default="cosine", choices=["linear", "cosine"])
    parser.add_argument("--label_smoothing", default=0.1, type=float)
    parser.add_argument("--dropout", default=0.1, type=float)
    
    # 数据配置
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    
    # 评估和保存
    parser.add_argument("--eval_steps", default=100, type=int)
    parser.add_argument("--save_steps", default=500, type=int)
    parser.add_argument("--early_stop", default=5, type=int)
    parser.add_argument("--eval_full", default=1, type=int)
    
    # 其他配置
    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)
    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
    parser.add_argument("--mean_verbalizer", default=True, type=bool)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--not_manual", default=False, type=int)
    
    return parser.parse_args()

def create_data_augmentation(text, tokenizer, aug_prob=0.15):
    """
    实现数据增强策略
    """
    if random.random() > aug_prob:
        return text
    
    # 随机mask一些token进行增强
    tokens = tokenizer.tokenize(text)
    if len(tokens) <= 2:
        return text
    
    # 随机选择15%的token进行mask
    num_to_mask = max(1, int(len(tokens) * 0.15))
    mask_indices = random.sample(range(1, len(tokens)-1), min(num_to_mask, len(tokens)-2))
    
    for idx in mask_indices:
        if random.random() < 0.8:  # 80%概率用[MASK]替换
            tokens[idx] = tokenizer.mask_token
        elif random.random() < 0.5:  # 10%概率用随机token替换
            tokens[idx] = random.choice(tokenizer.vocab.keys())
        # 10%概率保持不变
    
    return tokenizer.convert_tokens_to_string(tokens)

def get_advanced_scheduler(optimizer, num_training_steps, warmup_steps, scheduler_type="cosine"):
    """获取高级学习率调度器"""
    if scheduler_type == "cosine":
        return get_cosine_schedule_with_warmup(
            optimizer, 
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps
        )
    else:
        return get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps
        )

def main():
    args = get_optimized_args()
    
    # 设置分布式训练
    is_distributed, rank, world_size, gpu = setup_distributed()
    
    if rank == 0:
        print_info("🚀 启动高性能Large模型训练")
        print_info(f"使用模型: {args.model_name_or_path}")
        print_info(f"分布式训练: {is_distributed}, World Size: {world_size}")
        print_info(f"有效Batch Size: {args.batch_size * args.gradient_accumulation_steps * world_size}")
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建TensorBoard writer
    if rank == 0:
        writer = SummaryWriter(f'runs/large_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    
    # 加载数据和处理器
    processor = WOSProcessor()
    dataset = processor.get_examples(args.shot, args.seed, args.label_description)

    if rank == 0:
        print_info(f"训练数据: {len(dataset['train'])}")
        print_info(f"验证数据: {len(dataset['validation'])}")
        print_info(f"测试数据: {len(dataset['test'])}")

    # 加载模型和tokenizer
    from util.load_plm import load_plm_from_config
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # 创建模板和动词化器
    from openprompt.prompts import ManualTemplate
    from openprompt.prompts import SoftVerbalizer

    template = ManualTemplate(
        text='{"placeholder":"text_a"} 这是关于 {"mask"} 的内容。',
        tokenizer=tokenizer
    )

    # 创建层次化动词化器
    verbalizers = []
    for i in range(args.depth):
        verbalizer = SoftVerbalizer(
            tokenizer=tokenizer,
            plm=plm,
            num_classes=len(processor.hier_mapping[i]) if i < len(processor.hier_mapping) else len(processor.all_labels)
        )
        verbalizers.append(verbalizer)

    # 创建层次化模型
    prompt_model = HierarchicalPromptModel(
        plm=plm,
        template=template,
        verbalizers=verbalizers,
        freeze_plm=args.freeze_plm,
        plm_eval_mode=args.plm_eval_mode
    )

    # 设置设备和分布式
    if is_distributed:
        torch.cuda.set_device(gpu)
        prompt_model = prompt_model.cuda(gpu)
        prompt_model = DDP(prompt_model, device_ids=[gpu], find_unused_parameters=True)
    else:
        prompt_model = prompt_model.cuda()

    # 创建数据加载器
    from torch.utils.data import DataLoader
    from openprompt import PromptDataLoader

    train_sampler = DistributedSampler(dataset['train']) if is_distributed else None
    val_sampler = DistributedSampler(dataset['validation']) if is_distributed else None

    train_dataloader = PromptDataLoader(
        dataset=dataset['train'],
        template=template,
        tokenizer=tokenizer,
        tokenizer_wrapper_class=WrapperClass,
        max_seq_length=args.max_seq_lens,
        batch_size=args.batch_size,
        shuffle=(train_sampler is None),
        sampler=train_sampler,
        teacher_forcing=False,
        predict_eos_token=False,
        truncate_method="head"
    )

    validation_dataloader = PromptDataLoader(
        dataset=dataset['validation'],
        template=template,
        tokenizer=tokenizer,
        tokenizer_wrapper_class=WrapperClass,
        max_seq_length=args.max_seq_lens,
        batch_size=args.batch_size,
        shuffle=False,
        sampler=val_sampler,
        teacher_forcing=False,
        predict_eos_token=False,
        truncate_method="head"
    )

    # 设置优化器
    no_decay = ["bias", "LayerNorm.weight"]
    optimizer_grouped_parameters = [
        {
            "params": [p for n, p in prompt_model.named_parameters()
                      if not any(nd in n for nd in no_decay) and "verbalizer" not in n],
            "weight_decay": args.weight_decay,
            "lr": args.lr
        },
        {
            "params": [p for n, p in prompt_model.named_parameters()
                      if any(nd in n for nd in no_decay) and "verbalizer" not in n],
            "weight_decay": 0.0,
            "lr": args.lr
        },
        {
            "params": [p for n, p in prompt_model.named_parameters() if "verbalizer" in n],
            "weight_decay": args.weight_decay,
            "lr": args.lr2
        }
    ]

    optimizer = torch.optim.AdamW(optimizer_grouped_parameters, eps=1e-8)

    # 计算训练步数
    num_training_steps = len(train_dataloader) * args.max_epochs // args.gradient_accumulation_steps
    warmup_steps = int(num_training_steps * args.warmup_ratio)

    # 设置学习率调度器
    scheduler = get_advanced_scheduler(
        optimizer, num_training_steps, warmup_steps, args.scheduler_type
    )

    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if args.use_fp16 else None

    if rank == 0:
        print_info(f"总训练步数: {num_training_steps}")
        print_info(f"预热步数: {warmup_steps}")
        print_info(f"使用混合精度: {args.use_fp16}")

    # 训练循环
    best_macro_f1 = 0
    best_micro_f1 = 0
    early_stop_count = 0
    global_step = 0

    # 生成运行标识符
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    run_name = f"{timestamp}-large-lr{args.lr}-batch{args.batch_size}-epochs{args.max_epochs}"

    for epoch in range(args.max_epochs):
        if rank == 0:
            print_info(f"开始第 {epoch + 1}/{args.max_epochs} 轮训练")

        if is_distributed:
            train_sampler.set_epoch(epoch)

        prompt_model.train()
        total_loss = 0
        loss_details = [0, 0, 0, 0]  # [分类损失, LM损失, 约束损失, 对比损失]

        progress_bar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}", disable=(rank != 0))

        for step, batch in enumerate(progress_bar):
            # 数据增强
            if hasattr(batch, 'input_ids') and random.random() < 0.1:  # 10%概率进行数据增强
                # 这里可以添加更复杂的数据增强逻辑
                pass

            # 将数据移到GPU
            batch = {k: v.cuda(gpu) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

            with torch.cuda.amp.autocast(enabled=args.use_fp16):
                logits, loss, cur_loss_details = prompt_model(batch)
                loss = loss / args.gradient_accumulation_steps

            # 反向传播
            if args.use_fp16:
                scaler.scale(loss).backward()
            else:
                loss.backward()

            total_loss += loss.item()
            for i, detail_loss in enumerate(cur_loss_details):
                loss_details[i] += detail_loss

            # 梯度累积
            if (step + 1) % args.gradient_accumulation_steps == 0:
                if args.use_fp16:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)
                    optimizer.step()

                scheduler.step()
                optimizer.zero_grad()
                global_step += 1

                # 记录到TensorBoard
                if rank == 0 and global_step % 10 == 0:
                    writer.add_scalar('Train/Loss', loss.item() * args.gradient_accumulation_steps, global_step)
                    writer.add_scalar('Train/LR', scheduler.get_last_lr()[0], global_step)
                    for i, name in enumerate(['Classification', 'LM', 'Constraint', 'Contrastive']):
                        if loss_details[i] > 0:
                            writer.add_scalar(f'Train/Loss_{name}', loss_details[i], global_step)

                # 定期评估
                if global_step % args.eval_steps == 0:
                    eval_results = evaluate_model(prompt_model, validation_dataloader, processor, rank)

                    if rank == 0:
                        macro_f1 = eval_results.get('macro_f1', 0)
                        micro_f1 = eval_results.get('micro_f1', 0)

                        writer.add_scalar('Val/Macro_F1', macro_f1, global_step)
                        writer.add_scalar('Val/Micro_F1', micro_f1, global_step)

                        print_info(f"Step {global_step}: Macro F1: {macro_f1:.4f}, Micro F1: {micro_f1:.4f}")

                        # 保存最佳模型
                        if macro_f1 > best_macro_f1:
                            best_macro_f1 = macro_f1
                            save_path = f"ckpts/{run_name}-macro.ckpt"
                            torch.save(prompt_model.state_dict(), save_path)
                            early_stop_count = 0

                        if micro_f1 > best_micro_f1:
                            best_micro_f1 = micro_f1
                            save_path = f"ckpts/{run_name}-micro.ckpt"
                            torch.save(prompt_model.state_dict(), save_path)

                        early_stop_count += 1

            # 更新进度条
            if rank == 0:
                progress_bar.set_postfix({
                    'loss': f'{total_loss/(step+1):.4f}',
                    'lr': f'{scheduler.get_last_lr()[0]:.2e}'
                })

        # 每轮结束后的评估
        if rank == 0:
            avg_loss = total_loss / len(train_dataloader)
            print_info(f"Epoch {epoch+1} 平均损失: {avg_loss:.4f}")

            # 早停检查
            if early_stop_count >= args.early_stop:
                print_info(f"早停触发，最佳 Macro F1: {best_macro_f1:.4f}, Micro F1: {best_micro_f1:.4f}")
                break

    # 最终评估
    if rank == 0:
        print_info("开始最终测试评估...")
        test_dataloader = PromptDataLoader(
            dataset=dataset['test'],
            template=template,
            tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass,
            max_seq_length=args.max_seq_lens,
            batch_size=args.batch_size,
            shuffle=False,
            teacher_forcing=False,
            predict_eos_token=False,
            truncate_method="head"
        )

        # 加载最佳macro模型进行测试
        best_macro_path = f"ckpts/{run_name}-macro.ckpt"
        if os.path.exists(best_macro_path):
            prompt_model.load_state_dict(torch.load(best_macro_path))
            test_results = evaluate_model(prompt_model, test_dataloader, processor, rank)
            print_info(f"最佳Macro模型测试结果: {test_results}")

        # 保存结果
        with open(args.result_file, 'a', encoding='utf-8') as f:
            f.write(f"\n====================\n")
            f.write(f"Large模型训练完成 - {timestamp}\n")
            f.write(f"模型: {args.model_name_or_path}\n")
            f.write(f"最佳 Macro F1: {best_macro_f1:.6f}\n")
            f.write(f"最佳 Micro F1: {best_micro_f1:.6f}\n")
            f.write(f"训练参数: lr={args.lr}, batch_size={args.batch_size}, epochs={args.max_epochs}\n")

        writer.close()
        print_info("训练完成！")

def evaluate_model(model, dataloader, processor, rank):
    """评估模型性能"""
    if rank != 0:
        return {}

    model.eval()
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            batch = {k: v.cuda() if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            logits, _ = model(batch)

            # 获取预测结果
            preds = torch.argmax(logits, dim=-1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(batch['label'].cpu().numpy())

    # 计算指标
    from sklearn.metrics import f1_score, accuracy_score
    macro_f1 = f1_score(all_labels, all_preds, average='macro')
    micro_f1 = f1_score(all_labels, all_preds, average='micro')
    accuracy = accuracy_score(all_labels, all_preds)

    return {
        'macro_f1': macro_f1,
        'micro_f1': micro_f1,
        'accuracy': accuracy
    }

if __name__ == "__main__":
    main()
