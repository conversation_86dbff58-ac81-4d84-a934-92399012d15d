#!/usr/bin/env python3
"""
终极训练脚本 - 集成所有优化技术
实现最佳性能的chinese-roberta-wwm-ext-large模型训练
"""

import os
import sys
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
import argparse
import logging
from datetime import datetime
from tqdm import tqdm
import json
import pickle
import random
import numpy as np
from transformers import (
    AutoTokenizer, 
    AutoModel, 
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup
)
from torch.utils.tensorboard import SummaryWriter
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append('/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL')
from processor import *
from models.hierVerb import HierarchicalPromptModel
from util.utils import print_info, set_seed
from data_augmentation import AdvancedDataAugmentation
from advanced_loss import AdvancedLossManager, create_optimized_loss_config

# 设置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class UltimateTrainer:
    """终极训练器 - 集成所有优化技术"""
    
    def __init__(self, args):
        self.args = args
        self.setup_distributed()
        self.setup_logging()
        self.setup_random_seed()
        
        # 初始化组件
        self.tokenizer = None
        self.model = None
        self.loss_manager = None
        self.data_augmenter = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None
        
        # 训练状态
        self.best_macro_f1 = 0
        self.best_micro_f1 = 0
        self.global_step = 0
        self.early_stop_count = 0
        
        # 生成运行标识符
        self.run_name = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_ultimate_large"
        
    def setup_distributed(self):
        """设置分布式训练"""
        if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
            self.rank = int(os.environ["RANK"])
            self.world_size = int(os.environ['WORLD_SIZE'])
            self.gpu = int(os.environ['LOCAL_RANK'])
            self.is_distributed = True
            
            torch.cuda.set_device(self.gpu)
            dist.init_process_group(backend='nccl', init_method='env://', 
                                  world_size=self.world_size, rank=self.rank)
            dist.barrier()
        else:
            self.is_distributed = False
            self.rank = 0
            self.world_size = 1
            self.gpu = 0
    
    def setup_logging(self):
        """设置日志和TensorBoard"""
        if self.rank == 0:
            self.writer = SummaryWriter(f'runs/{self.run_name}')
            print_info("🚀 启动终极Large模型训练")
            print_info(f"运行标识: {self.run_name}")
            print_info(f"分布式训练: {self.is_distributed}, World Size: {self.world_size}")
    
    def setup_random_seed(self):
        """设置随机种子"""
        set_seed(self.args.seed)
    
    def load_model_and_tokenizer(self):
        """加载模型和tokenizer"""
        if self.rank == 0:
            print_info(f"加载模型: {self.args.model_name_or_path}")
        
        # 加载tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.args.model_name_or_path)
        
        # 加载模型
        from util.load_plm import load_plm_from_config
        plm, tokenizer, model_config, WrapperClass = load_plm_from_config(self.args, self.args.model_name_or_path)
        
        # 创建模板和动词化器
        from openprompt.prompts import ManualTemplate, SoftVerbalizer
        
        template = ManualTemplate(
            text='{"placeholder":"text_a"} 这是关于 {"mask"} 的内容。',
            tokenizer=tokenizer
        )
        
        # 创建层次化动词化器
        processor = WOSProcessor()
        verbalizers = []
        for i in range(self.args.depth):
            num_classes = len(processor.hier_mapping[i]) if i < len(processor.hier_mapping) else len(processor.all_labels)
            verbalizer = SoftVerbalizer(
                tokenizer=tokenizer,
                plm=plm,
                num_classes=num_classes
            )
            verbalizers.append(verbalizer)
        
        # 创建层次化模型
        self.model = HierarchicalPromptModel(
            plm=plm,
            template=template,
            verbalizers=verbalizers,
            freeze_plm=self.args.freeze_plm,
            plm_eval_mode=self.args.plm_eval_mode
        )
        
        # 设置设备和分布式
        if self.is_distributed:
            self.model = self.model.cuda(self.gpu)
            self.model = DDP(self.model, device_ids=[self.gpu], find_unused_parameters=True)
        else:
            self.model = self.model.cuda()
        
        return template, WrapperClass, processor
    
    def setup_data_augmentation(self):
        """设置数据增强"""
        self.data_augmenter = AdvancedDataAugmentation(
            tokenizer=self.tokenizer,
            aug_prob=0.3
        )
        
        if self.rank == 0:
            print_info("数据增强器初始化完成")
    
    def setup_loss_manager(self):
        """设置损失函数管理器"""
        loss_config = create_optimized_loss_config(num_classes=134)
        
        # 根据args更新配置
        loss_config['loss_weights'].update({
            'lm': self.args.lm_alpha,
            'contrastive': self.args.contrastive_alpha,
            'focal': 0.4,
            'label_smoothing': self.args.label_smoothing
        })
        
        self.loss_manager = AdvancedLossManager(loss_config)
        
        if self.rank == 0:
            print_info("高级损失函数管理器初始化完成")
    
    def setup_optimizer_and_scheduler(self, num_training_steps):
        """设置优化器和调度器"""
        # 参数分组
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay) and "verbalizer" not in n],
                "weight_decay": self.args.weight_decay,
                "lr": self.args.lr
            },
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay) and "verbalizer" not in n],
                "weight_decay": 0.0,
                "lr": self.args.lr
            },
            {
                "params": [p for n, p in self.model.named_parameters() if "verbalizer" in n],
                "weight_decay": self.args.weight_decay,
                "lr": self.args.lr2
            }
        ]
        
        # 使用AdamW优化器
        self.optimizer = torch.optim.AdamW(
            optimizer_grouped_parameters, 
            eps=1e-8,
            betas=(0.9, 0.999)
        )
        
        # 计算预热步数
        warmup_steps = int(num_training_steps * self.args.warmup_ratio)
        
        # 设置学习率调度器
        if self.args.scheduler_type == "cosine":
            self.scheduler = get_cosine_schedule_with_warmup(
                self.optimizer, 
                num_warmup_steps=warmup_steps,
                num_training_steps=num_training_steps
            )
        else:
            self.scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=num_training_steps
            )
        
        # 混合精度训练
        if self.args.use_fp16:
            self.scaler = torch.cuda.amp.GradScaler()
        
        if self.rank == 0:
            print_info(f"优化器和调度器设置完成")
            print_info(f"总训练步数: {num_training_steps}, 预热步数: {warmup_steps}")
    
    def create_data_loaders(self, dataset, template, WrapperClass):
        """创建数据加载器"""
        from torch.utils.data import DataLoader
        from openprompt import PromptDataLoader
        
        # 数据增强
        if self.args.use_data_augmentation:
            augmented_train_data = self.data_augmenter.augment_dataset(
                dataset['train'], 
                augment_ratio=0.3
            )
            if self.rank == 0:
                print_info(f"数据增强: {len(dataset['train'])} -> {len(augmented_train_data)}")
            dataset['train'] = augmented_train_data
        
        # 创建采样器
        train_sampler = DistributedSampler(dataset['train']) if self.is_distributed else None
        val_sampler = DistributedSampler(dataset['validation']) if self.is_distributed else None
        
        # 创建数据加载器
        train_dataloader = PromptDataLoader(
            dataset=dataset['train'],
            template=template,
            tokenizer=self.tokenizer,
            tokenizer_wrapper_class=WrapperClass,
            max_seq_length=self.args.max_seq_lens,
            batch_size=self.args.batch_size,
            shuffle=(train_sampler is None),
            sampler=train_sampler,
            teacher_forcing=False,
            predict_eos_token=False,
            truncate_method="head"
        )
        
        validation_dataloader = PromptDataLoader(
            dataset=dataset['validation'],
            template=template,
            tokenizer=self.tokenizer,
            tokenizer_wrapper_class=WrapperClass,
            max_seq_length=self.args.max_seq_lens,
            batch_size=self.args.batch_size,
            shuffle=False,
            sampler=val_sampler,
            teacher_forcing=False,
            predict_eos_token=False,
            truncate_method="head"
        )
        
        return train_dataloader, validation_dataloader, train_sampler
    
    def train_epoch(self, train_dataloader, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        loss_details = {'total': 0, 'classification': 0, 'lm': 0, 'contrastive': 0}
        
        progress_bar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}", disable=(self.rank != 0))
        
        for step, batch in enumerate(progress_bar):
            # 将数据移到GPU
            batch = {k: v.cuda(self.gpu) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            with torch.cuda.amp.autocast(enabled=self.args.use_fp16):
                # 前向传播
                logits, base_loss, cur_loss_details = self.model(batch)
                
                # 使用高级损失函数
                embeddings = None  # 如果模型支持，可以获取embeddings
                lm_loss = torch.tensor(cur_loss_details[1]) if len(cur_loss_details) > 1 else None
                
                combined_loss, advanced_loss_details = self.loss_manager.compute_combined_loss(
                    logits=logits,
                    targets=batch['label'],
                    embeddings=embeddings,
                    lm_loss=lm_loss
                )
                
                loss = combined_loss / self.args.gradient_accumulation_steps
            
            # 反向传播
            if self.args.use_fp16:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            total_loss += loss.item()
            
            # 梯度累积
            if (step + 1) % self.args.gradient_accumulation_steps == 0:
                if self.args.use_fp16:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.max_grad_norm)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.max_grad_norm)
                    self.optimizer.step()
                
                self.scheduler.step()
                self.optimizer.zero_grad()
                self.global_step += 1
                
                # 记录到TensorBoard
                if self.rank == 0 and self.global_step % 10 == 0:
                    self.writer.add_scalar('Train/Loss', loss.item() * self.args.gradient_accumulation_steps, self.global_step)
                    self.writer.add_scalar('Train/LR', self.scheduler.get_last_lr()[0], self.global_step)
                    
                    for name, value in advanced_loss_details.items():
                        self.writer.add_scalar(f'Train/Loss_{name}', value, self.global_step)
                
                # 定期评估
                if self.global_step % self.args.eval_steps == 0:
                    self.evaluate_and_save()
            
            # 更新进度条
            if self.rank == 0:
                progress_bar.set_postfix({
                    'loss': f'{total_loss/(step+1):.4f}',
                    'lr': f'{self.scheduler.get_last_lr()[0]:.2e}'
                })
        
        return total_loss / len(train_dataloader)

def get_ultimate_args():
    """获取终极训练参数"""
    parser = argparse.ArgumentParser("终极Large模型训练")
    
    # 基础配置
    parser.add_argument("--model_name_or_path", 
                       default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large')
    parser.add_argument("--result_file", 
                       default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/ultimate_results.txt")
    
    # 优化的超参数（基于超参数优化结果）
    parser.add_argument("--lr", default=8e-6, type=float)
    parser.add_argument("--lr2", default=2e-5, type=float)
    parser.add_argument("--batch_size", default=20, type=int)
    parser.add_argument("--gradient_accumulation_steps", default=6, type=int)
    parser.add_argument("--max_epochs", default=12, type=int)
    parser.add_argument("--warmup_ratio", default=0.15, type=float)
    parser.add_argument("--weight_decay", default=0.015, type=float)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    
    # 损失函数权重
    parser.add_argument("--lm_alpha", default=0.55, type=float)
    parser.add_argument("--contrastive_alpha", default=0.15, type=float)
    parser.add_argument("--label_smoothing", default=0.12, type=float)
    
    # 高级训练技术
    parser.add_argument("--use_fp16", default=True, type=bool)
    parser.add_argument("--scheduler_type", default="cosine", choices=["linear", "cosine"])
    parser.add_argument("--use_data_augmentation", default=True, type=bool)
    
    # 其他参数
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--shot", default=30, type=int)
    parser.add_argument("--seed", default=171, type=int)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    parser.add_argument("--depth", default=7, type=int)
    parser.add_argument("--eval_steps", default=80, type=int)
    parser.add_argument("--early_stop", default=4, type=int)
    
    # 模型配置
    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--plm_eval_mode", default=False, type=bool)
    parser.add_argument("--mean_verbalizer", default=True, type=bool)
    parser.add_argument("--verbalizer", default="soft", type=str)
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--label_description", default=0, type=int)
    
    return parser.parse_args()

if __name__ == "__main__":
    args = get_ultimate_args()
    
    # 创建训练器
    trainer = UltimateTrainer(args)
    
    # 开始训练流程
    # 这里需要继续实现完整的训练流程...
