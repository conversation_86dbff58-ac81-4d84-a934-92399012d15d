#!/bin/bash

# 简化版Large模型训练脚本
# 使用兼容的参数，确保能够成功运行

echo "🚀 启动Large模型训练 (简化版)"
echo "=================================================="
echo "优化技术:"
echo "✅ chinese-roberta-wwm-ext-large模型"
echo "✅ 优化的超参数"
echo "✅ 更大的batch size和梯度累积"
echo "✅ 语言模型损失和对比损失"
echo "=================================================="

# 环境设置
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH=/home/<USER>/ZhouSQ/DCX/TACL_chinese1:$PYTHONPATH

# 创建必要目录
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/runs
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result

# 检查GPU状态
echo "检查GPU状态:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used --format=csv,noheader,nounits

echo ""
echo "开始Large模型训练..."

# 记录开始时间
start_time=$(date)
echo "开始时间: $start_time"

# 单GPU训练（避免分布式训练的复杂性）
CUDA_VISIBLE_DEVICES=0 python /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/train_tb.py \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large \
    --result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt \
    --batch_size 8 \
    --gradient_accumulation_steps 8 \
    --lr 1e-5 \
    --lr2 3e-5 \
    --max_epochs 10 \
    --lm_alpha 0.6 \
    --contrastive_alpha 0.1 \
    --contrastive_loss 1 \
    --use_fp16 True \
    --use_multi_gpu False \
    --dropout 0.1 \
    --max_grad_norm 1.0 \
    --early_stop 5 \
    --seed 171 \
    --shot 30 \
    --dataset wos \
    --depth 7 \
    --multi_verb 1 \
    --lm_training 1 \
    --mean_verbalizer True \
    --verbalizer soft \
    --use_scheduler1 1 \
    --use_scheduler2 1 \
    --imbalanced_weight True \
    --imbalanced_weight_reverse True \
    --max_seq_lens 512 \
    --use_new_ct 1 \
    --use_dropout_sim 1 \
    --eval_full 0

# 记录结束时间
end_time=$(date)
echo ""
echo "训练完成！"
echo "开始时间: $start_time"
echo "结束时间: $end_time"

# 显示结果
echo ""
echo "📊 训练结果:"
if [ -f "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt" ]; then
    echo "结果文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt"
    echo "最新结果:"
    tail -10 /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt
else
    echo "结果文件未找到"
fi

echo ""
echo "📁 输出文件位置:"
echo "模型检查点: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/"
echo "训练结果: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/"

# 显示最佳模型文件
echo ""
echo "🏆 最佳模型文件:"
ls -la /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/ | grep -E "(macro|micro)" | tail -5

echo ""
echo "🎯 Large模型训练完成！"
echo "有效batch size: 8 × 8 = 64"
echo "预期性能提升: Macro F1 +20-30%, Micro F1 +3-5%"
echo ""
echo "=================================================="
