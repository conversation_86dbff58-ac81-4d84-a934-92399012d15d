#!/usr/bin/env python3
"""
超参数优化脚本
使用贝叶斯优化等方法寻找最佳超参数组合
"""

import os
import sys
import json
import numpy as np
import torch
from typing import Dict, List, Tuple, Any
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import logging
from datetime import datetime

# 添加项目路径
sys.path.append('/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL')

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, base_config: Dict, n_trials: int = 50):
        self.base_config = base_config
        self.n_trials = n_trials
        self.best_params = None
        self.best_score = 0
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 创建Optuna study
        self.study = optuna.create_study(
            direction='maximize',
            sampler=TPESampler(seed=42),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
    
    def suggest_hyperparameters(self, trial: optuna.Trial) -> Dict:
        """建议超参数组合"""
        
        # 学习率优化
        lr = trial.suggest_float('lr', 5e-6, 5e-5, log=True)
        lr2 = trial.suggest_float('lr2', 1e-5, 1e-4, log=True)
        
        # 批次大小优化
        batch_size = trial.suggest_categorical('batch_size', [8, 16, 24, 32])
        gradient_accumulation_steps = trial.suggest_categorical('gradient_accumulation_steps', [2, 4, 8])
        
        # 训练策略优化
        max_epochs = trial.suggest_int('max_epochs', 8, 20)
        warmup_ratio = trial.suggest_float('warmup_ratio', 0.05, 0.2)
        weight_decay = trial.suggest_float('weight_decay', 0.001, 0.1, log=True)
        
        # 损失函数权重优化
        lm_alpha = trial.suggest_float('lm_alpha', 0.3, 0.8)
        contrastive_alpha = trial.suggest_float('contrastive_alpha', 0.05, 0.3)
        
        # 正则化参数优化
        dropout = trial.suggest_float('dropout', 0.05, 0.2)
        label_smoothing = trial.suggest_float('label_smoothing', 0.05, 0.2)
        
        # 调度器参数优化
        scheduler_type = trial.suggest_categorical('scheduler_type', ['linear', 'cosine'])
        
        # Focal Loss参数优化
        focal_alpha = trial.suggest_float('focal_alpha', 0.5, 2.0)
        focal_gamma = trial.suggest_float('focal_gamma', 1.0, 3.0)
        
        return {
            'lr': lr,
            'lr2': lr2,
            'batch_size': batch_size,
            'gradient_accumulation_steps': gradient_accumulation_steps,
            'max_epochs': max_epochs,
            'warmup_ratio': warmup_ratio,
            'weight_decay': weight_decay,
            'lm_alpha': lm_alpha,
            'contrastive_alpha': contrastive_alpha,
            'dropout': dropout,
            'label_smoothing': label_smoothing,
            'scheduler_type': scheduler_type,
            'focal_alpha': focal_alpha,
            'focal_gamma': focal_gamma
        }
    
    def objective(self, trial: optuna.Trial) -> float:
        """优化目标函数"""
        
        # 获取建议的超参数
        params = self.suggest_hyperparameters(trial)
        
        # 更新配置
        config = self.base_config.copy()
        config.update(params)
        
        try:
            # 运行训练并获取验证分数
            score = self.run_training_with_config(config, trial)
            
            # 记录结果
            self.logger.info(f"Trial {trial.number}: Score = {score:.4f}, Params = {params}")
            
            return score
            
        except Exception as e:
            self.logger.error(f"Trial {trial.number} failed: {str(e)}")
            return 0.0
    
    def run_training_with_config(self, config: Dict, trial: optuna.Trial) -> float:
        """使用给定配置运行训练"""
        
        # 这里应该调用实际的训练函数
        # 为了演示，我们使用一个模拟函数
        
        # 模拟训练过程
        import time
        import random
        
        # 模拟训练时间
        time.sleep(1)
        
        # 基于超参数计算模拟分数
        # 实际应用中，这里应该是真实的训练和验证过程
        base_score = 0.7
        
        # 学习率影响
        lr_factor = 1.0 - abs(config['lr'] - 2e-5) / 2e-5 * 0.1
        
        # 批次大小影响
        batch_factor = 1.0 if config['batch_size'] >= 16 else 0.95
        
        # 权重衰减影响
        wd_factor = 1.0 - abs(config['weight_decay'] - 0.01) / 0.01 * 0.05
        
        # 损失权重影响
        loss_factor = 1.0 - abs(config['lm_alpha'] - 0.6) / 0.6 * 0.05
        
        # 添加随机噪声
        noise = random.uniform(-0.02, 0.02)
        
        score = base_score * lr_factor * batch_factor * wd_factor * loss_factor + noise
        
        # 模拟早期停止
        if trial.should_prune():
            raise optuna.TrialPruned()
        
        return max(0.0, min(1.0, score))
    
    def optimize(self) -> Dict:
        """执行超参数优化"""
        
        self.logger.info(f"开始超参数优化，共 {self.n_trials} 次试验")
        
        # 执行优化
        self.study.optimize(self.objective, n_trials=self.n_trials)
        
        # 获取最佳参数
        self.best_params = self.study.best_params
        self.best_score = self.study.best_value
        
        self.logger.info(f"优化完成！最佳分数: {self.best_score:.4f}")
        self.logger.info(f"最佳参数: {self.best_params}")
        
        return self.best_params
    
    def save_results(self, output_path: str):
        """保存优化结果"""
        
        results = {
            'best_score': self.best_score,
            'best_params': self.best_params,
            'n_trials': self.n_trials,
            'timestamp': datetime.now().isoformat(),
            'all_trials': []
        }
        
        # 保存所有试验结果
        for trial in self.study.trials:
            trial_data = {
                'number': trial.number,
                'value': trial.value,
                'params': trial.params,
                'state': trial.state.name
            }
            results['all_trials'].append(trial_data)
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"结果已保存到: {output_path}")

def get_base_config() -> Dict:
    """获取基础配置"""
    return {
        'model_name_or_path': '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large',
        'dataset': 'wos',
        'shot': 30,
        'seed': 171,
        'max_seq_lens': 512,
        'use_fp16': True,
        'use_multi_gpu': True,
        'early_stop': 5,
        'eval_steps': 100,
        'save_steps': 500,
        
        # 损失函数配置
        'use_focal_loss': True,
        'use_label_smoothing': True,
        'use_contrastive_loss': True,
        'use_hierarchical_loss': True,
        
        # 其他固定参数
        'multi_label': 0,
        'multi_verb': 1,
        'depth': 7,
        'constraint_loss': 0,
        'freeze_plm': 0,
        'mean_verbalizer': True,
        'verbalizer': 'soft',
        'template_id': 0
    }

def create_optimized_training_script(best_params: Dict, output_path: str):
    """根据最佳参数创建优化的训练脚本"""
    
    script_content = f"""#!/bin/bash

# 基于超参数优化结果的最佳训练脚本
# 生成时间: {datetime.now().isoformat()}

echo "🚀 启动优化后的Large模型训练"
echo "使用最佳超参数组合"

export CUDA_VISIBLE_DEVICES=0,1,2,3
export NCCL_DEBUG=INFO

python -m torch.distributed.launch \\
    --nproc_per_node=4 \\
    --master_port=29500 \\
    /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/train_large_optimized.py \\
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large \\
    --batch_size {best_params.get('batch_size', 16)} \\
    --gradient_accumulation_steps {best_params.get('gradient_accumulation_steps', 4)} \\
    --lr {best_params.get('lr', 1e-5)} \\
    --lr2 {best_params.get('lr2', 3e-5)} \\
    --max_epochs {best_params.get('max_epochs', 15)} \\
    --warmup_ratio {best_params.get('warmup_ratio', 0.1)} \\
    --weight_decay {best_params.get('weight_decay', 0.01)} \\
    --lm_alpha {best_params.get('lm_alpha', 0.6)} \\
    --contrastive_alpha {best_params.get('contrastive_alpha', 0.1)} \\
    --dropout {best_params.get('dropout', 0.1)} \\
    --label_smoothing {best_params.get('label_smoothing', 0.1)} \\
    --scheduler_type {best_params.get('scheduler_type', 'cosine')} \\
    --focal_alpha {best_params.get('focal_alpha', 1.0)} \\
    --focal_gamma {best_params.get('focal_gamma', 2.0)} \\
    --use_fp16 True \\
    --contrastive_loss 1 \\
    --eval_steps 100 \\
    --save_steps 500 \\
    --early_stop 5 \\
    --seed 171

echo "优化训练完成！"
"""
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(output_path, 0o755)
    
    print(f"优化训练脚本已保存到: {output_path}")

def main():
    """主函数"""
    
    # 获取基础配置
    base_config = get_base_config()
    
    # 创建优化器
    optimizer = HyperparameterOptimizer(base_config, n_trials=30)
    
    # 执行优化
    best_params = optimizer.optimize()
    
    # 保存结果
    results_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/hyperparameter_optimization_results.json"
    optimizer.save_results(results_path)
    
    # 创建优化的训练脚本
    script_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/run_optimized_training.sh"
    create_optimized_training_script(best_params, script_path)
    
    print("\n" + "="*50)
    print("超参数优化完成！")
    print(f"最佳分数: {optimizer.best_score:.4f}")
    print("最佳参数:")
    for key, value in best_params.items():
        print(f"  {key}: {value}")
    print("="*50)

if __name__ == "__main__":
    main()
