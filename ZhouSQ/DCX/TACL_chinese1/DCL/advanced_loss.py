#!/usr/bin/env python3
"""
高级损失函数模块
实现多种先进的损失函数组合以提升模型性能
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional

class FocalLoss(nn.Module):
    """Focal Loss - 解决类别不平衡问题"""
    
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super(<PERSON>ocal<PERSON><PERSON>, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingLoss(nn.Module):
    """标签平滑损失 - 提升模型泛化能力"""
    
    def __init__(self, num_classes, smoothing=0.1):
        super(LabelSmoothingLoss, self).__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, inputs, targets):
        log_probs = F.log_softmax(inputs, dim=-1)
        targets_one_hot = torch.zeros_like(log_probs).scatter_(1, targets.unsqueeze(1), 1)
        targets_smooth = targets_one_hot * self.confidence + (1 - targets_one_hot) * self.smoothing / (self.num_classes - 1)
        loss = (-targets_smooth * log_probs).sum(dim=-1).mean()
        return loss

class ContrastiveLoss(nn.Module):
    """对比学习损失 - 增强特征表示"""
    
    def __init__(self, temperature=0.07, margin=0.5):
        super(ContrastiveLoss, self).__init__()
        self.temperature = temperature
        self.margin = margin
    
    def forward(self, embeddings, labels):
        """
        Args:
            embeddings: [batch_size, hidden_dim]
            labels: [batch_size]
        """
        batch_size = embeddings.size(0)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(embeddings, embeddings.T) / self.temperature
        
        # 创建标签掩码
        labels = labels.unsqueeze(1)
        mask = torch.eq(labels, labels.T).float()
        
        # 移除对角线（自己与自己的相似度）
        mask = mask - torch.eye(batch_size, device=mask.device)
        
        # 计算正样本和负样本的损失
        exp_sim = torch.exp(similarity_matrix)
        sum_exp_sim = torch.sum(exp_sim * (1 - torch.eye(batch_size, device=mask.device)), dim=1, keepdim=True)
        
        log_prob = similarity_matrix - torch.log(sum_exp_sim)
        mean_log_prob_pos = torch.sum(mask * log_prob, dim=1) / (torch.sum(mask, dim=1) + 1e-8)
        
        loss = -mean_log_prob_pos.mean()
        return loss

class HierarchicalLoss(nn.Module):
    """层次化损失 - 针对层次分类任务"""
    
    def __init__(self, hierarchy_weights=None, level_weights=None):
        super(HierarchicalLoss, self).__init__()
        self.hierarchy_weights = hierarchy_weights or [1.0, 0.8, 0.6, 0.4, 0.2]
        self.level_weights = level_weights or [1.0] * 7
    
    def forward(self, logits_list, targets_list):
        """
        Args:
            logits_list: List of logits for each level
            targets_list: List of targets for each level
        """
        total_loss = 0
        
        for i, (logits, targets) in enumerate(zip(logits_list, targets_list)):
            if logits is not None and targets is not None:
                level_loss = F.cross_entropy(logits, targets)
                weight = self.level_weights[i] if i < len(self.level_weights) else 1.0
                total_loss += weight * level_loss
        
        return total_loss

class KnowledgeDistillationLoss(nn.Module):
    """知识蒸馏损失 - 从大模型向小模型传递知识"""
    
    def __init__(self, temperature=4.0, alpha=0.7):
        super(KnowledgeDistillationLoss, self).__init__()
        self.temperature = temperature
        self.alpha = alpha
    
    def forward(self, student_logits, teacher_logits, targets):
        """
        Args:
            student_logits: 学生模型的输出
            teacher_logits: 教师模型的输出
            targets: 真实标签
        """
        # 硬标签损失
        hard_loss = F.cross_entropy(student_logits, targets)
        
        # 软标签损失
        soft_loss = F.kl_div(
            F.log_softmax(student_logits / self.temperature, dim=-1),
            F.softmax(teacher_logits / self.temperature, dim=-1),
            reduction='batchmean'
        ) * (self.temperature ** 2)
        
        # 组合损失
        total_loss = self.alpha * hard_loss + (1 - self.alpha) * soft_loss
        return total_loss

class AdvancedLossManager:
    """高级损失函数管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.losses = {}
        
        # 初始化各种损失函数
        if config.get('use_focal_loss', False):
            self.losses['focal'] = FocalLoss(
                alpha=config.get('focal_alpha', 1.0),
                gamma=config.get('focal_gamma', 2.0)
            )
        
        if config.get('use_label_smoothing', False):
            self.losses['label_smoothing'] = LabelSmoothingLoss(
                num_classes=config.get('num_classes', 134),
                smoothing=config.get('smoothing', 0.1)
            )
        
        if config.get('use_contrastive_loss', False):
            self.losses['contrastive'] = ContrastiveLoss(
                temperature=config.get('contrastive_temperature', 0.07),
                margin=config.get('contrastive_margin', 0.5)
            )
        
        if config.get('use_hierarchical_loss', False):
            self.losses['hierarchical'] = HierarchicalLoss(
                hierarchy_weights=config.get('hierarchy_weights'),
                level_weights=config.get('level_weights')
            )
        
        # 损失权重
        self.loss_weights = config.get('loss_weights', {
            'classification': 1.0,
            'lm': 0.6,
            'focal': 0.3,
            'label_smoothing': 0.2,
            'contrastive': 0.1,
            'hierarchical': 0.4
        })
    
    def compute_combined_loss(self, 
                            logits: torch.Tensor,
                            targets: torch.Tensor,
                            embeddings: Optional[torch.Tensor] = None,
                            lm_loss: Optional[torch.Tensor] = None,
                            logits_list: Optional[List[torch.Tensor]] = None,
                            targets_list: Optional[List[torch.Tensor]] = None) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算组合损失
        
        Returns:
            total_loss: 总损失
            loss_details: 各项损失的详细信息
        """
        total_loss = 0
        loss_details = {}
        
        # 基础分类损失
        if 'focal' in self.losses:
            focal_loss = self.losses['focal'](logits, targets)
            total_loss += self.loss_weights.get('focal', 0.3) * focal_loss
            loss_details['focal'] = focal_loss.item()
        elif 'label_smoothing' in self.losses:
            smooth_loss = self.losses['label_smoothing'](logits, targets)
            total_loss += self.loss_weights.get('label_smoothing', 0.2) * smooth_loss
            loss_details['label_smoothing'] = smooth_loss.item()
        else:
            # 标准交叉熵损失
            ce_loss = F.cross_entropy(logits, targets)
            total_loss += self.loss_weights.get('classification', 1.0) * ce_loss
            loss_details['classification'] = ce_loss.item()
        
        # 语言模型损失
        if lm_loss is not None:
            total_loss += self.loss_weights.get('lm', 0.6) * lm_loss
            loss_details['lm'] = lm_loss.item()
        
        # 对比学习损失
        if 'contrastive' in self.losses and embeddings is not None:
            contrastive_loss = self.losses['contrastive'](embeddings, targets)
            total_loss += self.loss_weights.get('contrastive', 0.1) * contrastive_loss
            loss_details['contrastive'] = contrastive_loss.item()
        
        # 层次化损失
        if 'hierarchical' in self.losses and logits_list is not None and targets_list is not None:
            hierarchical_loss = self.losses['hierarchical'](logits_list, targets_list)
            total_loss += self.loss_weights.get('hierarchical', 0.4) * hierarchical_loss
            loss_details['hierarchical'] = hierarchical_loss.item()
        
        return total_loss, loss_details
    
    def update_loss_weights(self, epoch: int, total_epochs: int):
        """动态调整损失权重"""
        # 在训练过程中逐渐减少辅助损失的权重
        decay_factor = 1.0 - (epoch / total_epochs) * 0.5
        
        if 'contrastive' in self.loss_weights:
            self.loss_weights['contrastive'] *= decay_factor
        
        if 'lm' in self.loss_weights:
            # 语言模型损失在后期可以适当减少
            self.loss_weights['lm'] = max(0.3, self.loss_weights['lm'] * decay_factor)

def create_optimized_loss_config(num_classes: int = 134) -> Dict:
    """创建优化的损失函数配置"""
    return {
        'use_focal_loss': True,
        'focal_alpha': 1.0,
        'focal_gamma': 2.0,
        
        'use_label_smoothing': True,
        'smoothing': 0.1,
        'num_classes': num_classes,
        
        'use_contrastive_loss': True,
        'contrastive_temperature': 0.07,
        'contrastive_margin': 0.5,
        
        'use_hierarchical_loss': True,
        'hierarchy_weights': [1.0, 0.8, 0.6, 0.4, 0.2],
        'level_weights': [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4],
        
        'loss_weights': {
            'classification': 0.6,
            'lm': 0.5,
            'focal': 0.4,
            'label_smoothing': 0.3,
            'contrastive': 0.2,
            'hierarchical': 0.5
        }
    }

if __name__ == "__main__":
    # 测试损失函数
    config = create_optimized_loss_config()
    loss_manager = AdvancedLossManager(config)
    
    # 模拟数据
    batch_size, num_classes, hidden_dim = 32, 134, 768
    logits = torch.randn(batch_size, num_classes)
    targets = torch.randint(0, num_classes, (batch_size,))
    embeddings = torch.randn(batch_size, hidden_dim)
    lm_loss = torch.tensor(0.5)
    
    # 计算损失
    total_loss, loss_details = loss_manager.compute_combined_loss(
        logits=logits,
        targets=targets,
        embeddings=embeddings,
        lm_loss=lm_loss
    )
    
    print(f"总损失: {total_loss.item():.4f}")
    print("损失详情:")
    for name, value in loss_details.items():
        print(f"  {name}: {value:.4f}")
