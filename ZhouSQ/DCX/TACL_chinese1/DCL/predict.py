import torch
import pickle
from openprompt import PromptD<PERSON><PERSON>oader
from openprompt.prompts import ManualTemplate
from openprompt.data_utils import InputExample
from openprompt.plms import load_plm
from openprompt.utils.reproduciblity import set_seed
# from openprompt.verbalizers import SoftVerbalizer
from openprompt.prompts import SoftVerbalizer, ManualTemplate
from models.topk_chy import HierVerbPromptForClassification  # 自定义模型
from processor import PROCESSOR
from util.utils import load_plm_from_config
from util.data_loader import SinglePathPromptDataLoader
import os
import argparse
import logging
import numpy as np
import json
from collections import defaultdict


class HierarchicalTextClassifier:
    """分层文本分类器 - 用于预测单条文本数据"""

    def __init__(self, model_ckpt_path, embedding_pkl_path, args=None):
        """
        初始化分类器

        Args:
            model_ckpt_path: 训练好的模型检查点路径
            embedding_pkl_path: 训练集嵌入文件路径
            args: 模型参数配置
        """
        self.model_ckpt_path = model_ckpt_path
        self.embedding_pkl_path = embedding_pkl_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.use_cuda = torch.cuda.is_available()

        # 如果没有提供args，使用默认配置
        if args is None:
            args = self._get_default_args()
        self.args = args

        # 初始化模型组件
        self._initialize_model()

    def _get_default_args(self):
        """获取默认参数配置"""
        from argparse import Namespace
        return Namespace(
            model='bert',
            model_name_or_path='/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext',
            dataset='wos',
            shot=30,
            seed=171,
            multi_mask=1,
            freeze_plm=0,
            plm_eval_mode=False,
            template_id=0,
            max_seq_lens=512,
            depth=7,  # 将在初始化时根据processor更新
            # 添加缺失的参数
            dropout=0.1,
            shuffle=0,
            contrastive_logits=1,
            constraint_loss=0,
            cs_mode=0,
            eval_mode=0,
            use_hier_mean=1,
            multi_label=0,
            multi_verb=1,
            use_scheduler1=1,
            use_scheduler2=1,
            constraint_alpha=-1,
            imbalanced_weight=True,
            imbalanced_weight_reverse=True,
            device=0,
            lm_training=1,
            lr=5e-5,
            lr2=1e-4,
            max_grad_norm=1.0,
            use_new_ct=1,
            contrastive_loss=0,
            contrastive_alpha=0.99,
            contrastive_level=1,
            use_dropout_sim=0,
            batch_size=5,
            use_withoutWrappedLM=False,
            mean_verbalizer=True,
            lm_alpha=0.999,
            label_description=0,
            verbalizer="soft",
            not_manual=False,
            gradient_accumulation_steps=1,
            max_epochs=20,
            early_stop=10,
            eval_full=0
        )

    def _initialize_model(self):
        """初始化模型组件"""
        try:
            print("🔧 设置随机种子...")
            set_seed(self.args.seed)

            print("🔧 加载processor...")
            # 加载processor
            self.processor = PROCESSOR[self.args.dataset](shot=self.args.shot, seed=self.args.seed)
            self.args.depth = len(self.processor.hier_mapping) + 1
            self.label_list = self.processor.label_list

            print("🔧 加载PLM和tokenizer...")
            # 加载PLM和tokenizer
            self.plm, self.tokenizer, self.model_config, self.WrapperClass = load_plm_from_config(
                self.args, self.args.model_name_or_path)

            print("🔧 加载模板...")
            # 加载模板
            template_file = f"{self.args.dataset}_mask_template.txt" if self.args.multi_mask else "manual_template.txt"

            # 尝试多个可能的路径
            possible_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "template", template_file),  # 相对路径
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "template", template_file),  # 当前目录
            ]

            template_path = None
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                if os.path.exists(abs_path):
                    template_path = abs_path
                    print(f"✅ 找到模板文件: {template_path}")
                    break

            if template_path is None:
                # 如果都没有找到，创建默认模板
                template_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "template", template_file)
                os.makedirs(os.path.dirname(template_path), exist_ok=True)
                self._create_default_template(template_path)

            self.mytemplate = ManualTemplate(tokenizer=self.tokenizer).from_file(template_path, choice=self.args.template_id)
            print("✅ 模板加载完成")

            print("🔧 创建verbalizer列表...")
            # 创建verbalizer列表
            self.verbalizer_list = []
            for i in range(self.args.depth):
                self.verbalizer_list.append(SoftVerbalizer(tokenizer=self.tokenizer, model=self.plm, classes=self.label_list[i]))

            print("🔧 构建prompt模型...")
            # 构建prompt模型
            self.prompt_model = HierVerbPromptForClassification(
                plm=self.plm,
                template=self.mytemplate,
                verbalizer_list=self.verbalizer_list,
                freeze_plm=self.args.freeze_plm,
                args=self.args,
                processor=self.processor,
                plm_eval_mode=self.args.plm_eval_mode,
                use_cuda=self.use_cuda
            )

            print("🔧 移动模型到GPU...")
            if self.use_cuda:
                self.prompt_model = self.prompt_model.cuda()

            print("🔧 加载训练好的权重...")
            # 加载训练好的权重
            self.prompt_model.load_state_dict(torch.load(self.model_ckpt_path, map_location=self.device))
            self.prompt_model.eval()

            print("🔧 加载训练集嵌入...")
            # 加载训练集嵌入
            embedding_list = pickle.load(open(self.embedding_pkl_path, "rb"))
            self.embedding_store = {
                'embedding': torch.stack([torch.Tensor(item[1]) for item in embedding_list['embedding']]).to(self.device),
                'label': embedding_list['label']
            }

            print(f"✅ 模型初始化完成！支持{self.args.depth}层分层分类")
            print(f"📊 各层标签数量: {[len(labels) for labels in self.label_list]}")

        except Exception as e:
            print(f"❌ 初始化模型组件失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _create_default_template(self, template_path):
        """创建默认模板文件"""
        # 创建默认的分层模板
        text_mask = [f'{i + 1} level: {{"mask"}}' for i in range(self.args.depth)]
        template_text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'

        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_text)

        print(f"✅ 创建默认模板文件: {template_path}")

    def predict(self, input_text, topk=3, return_confidence=True):
        """
        预测单条文本的分层标签

        Args:
            input_text: 输入文本
            topk: 返回top-k个预测结果
            return_confidence: 是否返回置信度分数（默认True，确保Top-3都有置信度）

        Returns:
            dict: 包含预测结果的字典
        """
        # 构建输入样本（预测时不需要真实标签，使用占位符）
        example = InputExample(guid=0, text_a=input_text, label=0)

        # 创建数据加载器
        dataloader = SinglePathPromptDataLoader(
            dataset=[example],
            template=self.mytemplate,
            tokenizer=self.tokenizer,
            tokenizer_wrapper_class=self.WrapperClass,
            max_seq_length=self.args.max_seq_lens,
            decoder_max_length=3,
            batch_size=1,
            shuffle=False,
            teacher_forcing=False,
            predict_eos_token=False,
            truncate_method="tail",
            multi_gpu=False,
            mode='test'
        )

        # 执行预测
        with torch.no_grad():
            predictions = self.prompt_model.predict_topk(
                dataloader=dataloader,
                processor=self.processor,
                embedding_store=self.embedding_store,
                topk=topk,
                device=self.device
            )

        # 格式化输出结果
        result = self._format_prediction_result(predictions[0], input_text, return_confidence)
        return result

    def _format_prediction_result(self, prediction, input_text, return_confidence=False):
        """格式化预测结果"""
        result = {
            'input_text': input_text,
            'predictions': []
        }

        # 提取top-k预测路径
        if 'topk_full_paths' in prediction:
            for i, path in enumerate(prediction['topk_full_paths']):
                pred_item = {
                    'rank': i + 1,
                    'full_path': ' -> '.join(path) if isinstance(path, list) else str(path)
                }

                # 始终添加置信度信息（确保Top-3都有置信度）
                if 'topk_scores' in prediction:
                    pred_item['confidence'] = prediction['topk_scores'][i] if i < len(prediction['topk_scores']) else 0.0
                else:
                    # 如果没有置信度分数，使用默认值
                    pred_item['confidence'] = 1.0 / (i + 1)  # 简单的递减置信度

                result['predictions'].append(pred_item)

        # 添加最佳预测
        if result['predictions']:
            result['best_prediction'] = result['predictions'][0]

        return result

    def predict_batch(self, texts, topk=3, return_confidence=True, batch_size=32):
        """
        批量预测多条文本（优化版本，支持真正的批量处理）

        Args:
            texts: 文本列表
            topk: 返回top-k个预测结果
            return_confidence: 是否返回置信度分数（默认True，确保Top-3都有置信度）
            batch_size: 批处理大小

        Returns:
            list: 预测结果列表
        """
        if not texts:
            return []

        print(f"🔄 开始批量预测 {len(texts)} 条文本（批大小: {batch_size}）...")

        all_results = []

        # 分批处理
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            # print(f"   处理批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}...")

            # 构建批量输入样本
            examples = []
            for j, text in enumerate(batch_texts):
                examples.append(InputExample(guid=i+j, text_a=text, label=0))

            # 创建批量数据加载器
            batch_dataloader = SinglePathPromptDataLoader(
                dataset=examples,
                template=self.mytemplate,
                tokenizer=self.tokenizer,
                tokenizer_wrapper_class=self.WrapperClass,
                max_seq_length=self.args.max_seq_lens,
                decoder_max_length=3,
                batch_size=len(examples),  # 一次处理整个批次
                shuffle=False,
                teacher_forcing=False,
                predict_eos_token=False,
                truncate_method="tail",
                multi_gpu=False,
                mode='test'
            )

            # 执行批量预测
            with torch.no_grad():
                batch_predictions = self.prompt_model.predict_topk(
                    dataloader=batch_dataloader,
                    processor=self.processor,
                    embedding_store=self.embedding_store,
                    topk=topk,
                    device=self.device
                )

            # 格式化批量结果
            for j, (text, prediction) in enumerate(zip(batch_texts, batch_predictions)):
                result = self._format_prediction_result(prediction, text, return_confidence)
                all_results.append(result)

        print(f"✅ 批量预测完成！")
        return all_results


def predict_single_text(input_text, args, model_ckpt_path, embedding_pkl_path):
    """
    兼容性函数 - 预测单条文本（保持原有接口）

    Args:
        input_text: 输入文本
        args: 参数配置
        model_ckpt_path: 模型检查点路径
        embedding_pkl_path: 嵌入文件路径

    Returns:
        预测结果
    """
    classifier = HierarchicalTextClassifier(model_ckpt_path, embedding_pkl_path, args)
    result = classifier.predict(input_text, topk=6)
    return [result]  # 保持原有返回格式


def calculate_full_accuracy( args, model_ckpt_path, embedding_pkl_path, json_file_path):
    # 读取 JSON 文件

    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 建立 doc_token 到 doc_label 的映射字典
    # token_label_dict = {}
    token_label_dict = defaultdict(list)
    for item in data:
        doc_token = item.get('doc_token')
        doc_label = item.get('doc_label')
        if doc_token and doc_label:
            token_label_dict[doc_token].append(doc_label)

    total_count = len(token_label_dict)
    correct_count = 0

    # 遍历字典进行预测并计算准确率
    for doc_token, true_labels in token_label_dict.items():
        predictions = predict_single_text(doc_token, args, model_ckpt_path, embedding_pkl_path)
        predicted_labels = predictions[0].get("topk_full_paths")  # 假设预测结果的第一个元素是标签列表

        # 检查所有真实标签是否都在预测结果中
        if all(label in predicted_labels for label in true_labels):
            correct_count += 1

    # 计算完全准确率
    full_accuracy = correct_count / total_count if total_count > 0 else 0
    return full_accuracy

def calculate_full_accuracy_batch(args, model_ckpt_path, embedding_pkl_path, json_file_path):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    use_cuda = torch.cuda.is_available()

    # 设置随机种子
    set_seed(args.seed)

    # 加载 processor
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
    args.depth = len(processor.hier_mapping) + 1
    label_list = processor.label_list

    # 加载 PLM 和模板
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    template_file = f"{args.dataset}_mask_template.txt" if args.multi_mask else "manual_template.txt"
    template_path = os.path.join("template", template_file)

    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    # 构建 verbalizer list
    verbalizer_list = [SoftVerbalizer(tokenizer=tokenizer, model=plm, classes=label_list[i]) for i in range(args.depth)]

    # 构建 PromptModel
    prompt_model = HierVerbPromptForClassification(
        plm=plm,
        template=mytemplate,
        verbalizer_list=verbalizer_list,
        freeze_plm=args.freeze_plm,
        args=args,
        processor=processor,
        plm_eval_mode=args.plm_eval_mode,
        use_cuda=use_cuda
    )
    if use_cuda:
        prompt_model = prompt_model.cuda()

    # 加载模型权重
    prompt_model.load_state_dict(torch.load(model_ckpt_path, map_location=device))
    prompt_model.eval()

    # 加载 embedding store
    embedding_list = pickle.load(open(embedding_pkl_path, "rb"))
    embedding_store = {
        'embedding': torch.stack([torch.Tensor(item[1]) for item in embedding_list['embedding']]).to(device),
        'label': embedding_list['label']
    }

    # 读取数据并构造 examples
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    examples = []
    seen_tokens = set()
    token_to_true_labels = defaultdict(list)

    for item in data:
        doc_token = item.get("doc_token")
        doc_label = item.get("doc_label")
        if doc_token and doc_label:
            token_to_true_labels[doc_token].append(doc_label)
            if doc_token not in seen_tokens:
                examples.append(InputExample(text_a=doc_token))
                seen_tokens.add(doc_token)

    # 构建批量 DataLoader
    dataloader = SinglePathPromptDataLoader(
        dataset=examples,
        template=mytemplate,
        tokenizer=tokenizer,
        tokenizer_wrapper_class=WrapperClass,
        max_seq_length=args.max_seq_lens,
        decoder_max_length=3,
        batch_size=16,  # ✅ 你可以根据显存调大或调小
        shuffle=False,
        teacher_forcing=False,
        predict_eos_token=False,
        truncate_method="tail",
        multi_gpu=False,
        mode='test'
    )

    # 批量预测
    predictions = prompt_model.predict_topk(
        dataloader=dataloader,
        processor=processor,
        embedding_store=embedding_store,
        topk=6,
        device=device
    )

    # 计算准确率
    correct_count = 0
    for i, pred in enumerate(predictions):
        doc_token = examples[i].text_a
        true_labels = token_to_true_labels[doc_token]
        predicted_paths = pred["topk_full_paths"]

        # 判断是否包含所有真实标签路径（可以根据你真实标签结构自定义判断方式）
        if all(label in predicted_paths for label in true_labels):
            correct_count += 1

    full_accuracy = correct_count / len(predictions) if predictions else 0
    return full_accuracy
# 用法示例
if __name__ == "__main__":
    # 模型文件路径
    model_ckpt_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/wos-seed171-shot30-lr5e-05-lr20.0001-batch_size5-multi_mask1-use_new_ct1-cs_mode0-ctl0-contrastive_alpha0.99-shuffleFalse-constraint_loss0-multi_verb1-contrastive_level1--use_dropout_sim0-length6523-macro.ckpt"
    embedding_pkl_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/_30shot_none_171_embed_doc_0.pkl"

    # 方法1: 使用新的分类器类（推荐）
    print("🚀 初始化分层文本分类器...")
    classifier = HierarchicalTextClassifier(model_ckpt_path, embedding_pkl_path)

    # 测试文本
    # test_texts = [
    #     "在设计高铁内部的装饰时，要用到不同的平面图形，比如吸顶灯是圆形，车窗是长方形等。请写出下面的平面图形各有几条对称轴：正方形有( 　    )条；长方形有( 　    )条；等腰三角形有( 　    )条；正三角形有( 　    )条；圆有( 　    )条；等腰梯形有( 　    )条。",
    #     "机器学习是人工智能的一个重要分支，它通过算法让计算机能够从数据中学习并做出预测。",
    #     "量子计算利用量子力学原理进行信息处理，具有超越经典计算机的潜力。"
    # ]

    test_texts = [
        "已知$$2a+b$的算术平方根是4$$4a-b$的立方根是2求a-b的值",
    ]

    print("\n📝 单条文本预测示例:")
    result = classifier.predict(test_texts[0], topk=3, return_confidence=True)

    print(f"输入文本: {result['input_text'][:100]}...")
    print(f"最佳预测: {result['best_prediction']['full_path']}")
    print("\nTop-3 预测结果:")
    for pred in result['predictions']:
        print(f"  {pred['rank']}. {pred['full_path']}")
        if 'confidence' in pred:
            print(f"     置信度: {pred['confidence']:.4f}")

    # print("\n📚 批量预测示例:")
    # batch_results = classifier.predict_batch(test_texts[:2], topk=2)
    # for i, result in enumerate(batch_results):
    #     print(f"\n文本 {i+1}: {result['input_text'][:50]}...")
    #     print(f"预测: {result['best_prediction']['full_path']}")

    # print("\n" + "="*80)
    # print("💡 使用提示:")
    # print("1. 创建分类器实例: classifier = HierarchicalTextClassifier(model_path, embedding_path)")
    # print("2. 单条预测: result = classifier.predict(text, topk=3)")
    # print("3. 批量预测: results = classifier.predict_batch(texts, topk=3)")
    # print("4. 获取置信度: result = classifier.predict(text, return_confidence=True)")

    # 方法2: 使用原有接口（兼容性）
    # print("\n🔄 使用原有接口进行预测:")
    # from argparse import Namespace
    # args = Namespace(
    #     model='bert',
    #     model_name_or_path='/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese',
    #     dataset="wos",
    #     shot=16,
    #     seed=171,
    #     multi_mask=1,
    #     freeze_plm=0,
    #     plm_eval_mode=False,
    #     template_id=0,
    #     max_seq_lens=512,
    #     depth=9
    # )

    # legacy_result = predict_single_text(test_texts[0], args, model_ckpt_path, embedding_pkl_path)
    # print(f"原有接口预测结果: {legacy_result[0]['best_prediction']['full_path']}")
