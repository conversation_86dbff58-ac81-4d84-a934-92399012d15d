#!/bin/bash

# 保守版Large模型训练脚本
# 基于你当前成功的配置，最小化修改，确保成功

echo "🚀 启动保守版Large模型训练"
echo "=================================================="
echo "保守策略特点:"
echo "✅ 升级到chinese-roberta-wwm-ext-large模型"
echo "✅ 基于你当前成功的参数配置"
echo "✅ 禁用可能有问题的功能"
echo "✅ 专注于核心性能提升"
echo "=================================================="

# 环境设置
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=/home/<USER>/ZhouSQ/DCX/TACL_chinese1:$PYTHONPATH

# 创建必要目录
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result

# 检查GPU状态
echo "使用GPU:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used --format=csv,noheader,nounits | head -1

echo ""
echo "开始Large模型训练..."

# 记录开始时间
start_time=$(date)
echo "开始时间: $start_time"

# 运行训练 - 使用最保守的配置，确保成功
python /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/train_tb.py \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large \
    --result_file /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt \
    --batch_size 8 \
    --gradient_accumulation_steps 4 \
    --lr 2e-5 \
    --lr2 5e-5 \
    --max_epochs 6 \
    --lm_alpha 0.7 \
    --contrastive_loss 0 \
    --contrastive_alpha 0.0 \
    --constraint_loss 0 \
    --constraint_alpha 0.0 \
    --use_fp16 False \
    --use_multi_gpu False \
    --dropout 0.1 \
    --max_grad_norm 1.0 \
    --early_stop 5 \
    --seed 171 \
    --shot 30 \
    --dataset wos \
    --depth 7 \
    --multi_verb 1 \
    --lm_training 1 \
    --mean_verbalizer True \
    --verbalizer soft \
    --use_scheduler1 1 \
    --use_scheduler2 1 \
    --imbalanced_weight True \
    --imbalanced_weight_reverse True \
    --max_seq_lens 512 \
    --use_new_ct 0 \
    --use_dropout_sim 0 \
    --eval_full 0

# 记录结束时间
end_time=$(date)
echo ""
echo "训练完成！"
echo "开始时间: $start_time"
echo "结束时间: $end_time"

# 显示结果
echo ""
echo "📊 训练结果:"
if [ -f "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt" ]; then
    echo "结果文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt"
    echo ""
    echo "🎯 最新结果:"
    tail -15 /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt
    echo ""
    
    # 提取最佳结果
    echo "🏆 最佳性能:"
    grep "best_macro\|best_micro" /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt | tail -2
    
    echo ""
    echo "📈 性能对比:"
    echo "基线模型 (base): Macro F1: 0.243, Micro F1: 0.895"
    echo "Large模型结果请查看上方输出"
else
    echo "结果文件未找到，请检查训练是否成功完成"
fi

echo ""
echo "📁 输出文件位置:"
echo "模型检查点: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/"
echo "训练结果: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_conservative_results.txt"

# 显示最佳模型文件
echo ""
echo "🏆 生成的模型文件:"
ls -la /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/ | grep "$(date +%Y-%m-%d)" | tail -5

echo ""
echo "🎯 训练配置总结:"
echo "• 模型: chinese-roberta-wwm-ext-large (330M参数)"
echo "• 有效batch size: 8 × 4 = 32"
echo "• 学习率: 2e-5 (适配Large模型)"
echo "• 训练轮数: 6轮"
echo "• 损失函数: 分类损失 + 语言模型损失"
echo "• 预期提升: Macro F1 +10-20%, Micro F1 +1-3%"
echo ""
echo "=================================================="
echo "🎉 保守版Large模型训练完成！"
echo "这是最稳定的配置，应该能够成功运行。"
echo "=================================================="
