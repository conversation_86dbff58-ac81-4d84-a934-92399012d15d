#!/usr/bin/env python3
"""
高级数据增强模块
实现多种数据增强技术以提升模型泛化能力
"""

import random
import re
import jieba
import synonyms
import numpy as np
from typing import List, Dict, Tuple
import torch
from transformers import AutoTokenizer, AutoModel
import json

class AdvancedDataAugmentation:
    """高级数据增强类"""
    
    def __init__(self, tokenizer, aug_prob=0.3):
        self.tokenizer = tokenizer
        self.aug_prob = aug_prob
        
        # 初始化同义词库
        try:
            import synonyms
            self.use_synonyms = True
        except ImportError:
            print("Warning: synonyms库未安装，将跳过同义词替换")
            self.use_synonyms = False
        
        # 定义停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
        }
        
        # 定义标点符号
        self.punctuation = {'，', '。', '！', '？', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '《', '》'}
    
    def random_mask_augmentation(self, text: str, mask_prob: float = 0.15) -> str:
        """随机mask增强"""
        if random.random() > self.aug_prob:
            return text
        
        tokens = self.tokenizer.tokenize(text)
        if len(tokens) <= 2:
            return text
        
        # 计算要mask的token数量
        num_to_mask = max(1, int(len(tokens) * mask_prob))
        mask_indices = random.sample(range(1, len(tokens)-1), min(num_to_mask, len(tokens)-2))
        
        for idx in mask_indices:
            rand = random.random()
            if rand < 0.8:  # 80%概率用[MASK]替换
                tokens[idx] = self.tokenizer.mask_token
            elif rand < 0.9:  # 10%概率用随机token替换
                vocab_list = list(self.tokenizer.vocab.keys())
                tokens[idx] = random.choice(vocab_list)
            # 10%概率保持不变
        
        return self.tokenizer.convert_tokens_to_string(tokens)
    
    def synonym_replacement(self, text: str, replace_prob: float = 0.1) -> str:
        """同义词替换增强"""
        if not self.use_synonyms or random.random() > self.aug_prob:
            return text
        
        words = jieba.lcut(text)
        new_words = []
        
        for word in words:
            if (word not in self.stop_words and 
                word not in self.punctuation and 
                len(word) > 1 and 
                random.random() < replace_prob):
                
                # 获取同义词
                synonyms_list = synonyms.nearby(word)
                if synonyms_list and len(synonyms_list[0]) > 1:
                    # 选择相似度最高的同义词
                    synonym = synonyms_list[0][1]  # 第一个是原词，第二个是最相似的同义词
                    new_words.append(synonym)
                else:
                    new_words.append(word)
            else:
                new_words.append(word)
        
        return ''.join(new_words)
    
    def random_insertion(self, text: str, insert_prob: float = 0.1) -> str:
        """随机插入增强"""
        if random.random() > self.aug_prob:
            return text
        
        words = jieba.lcut(text)
        if len(words) < 3:
            return text
        
        # 随机选择插入位置
        insert_pos = random.randint(1, len(words)-1)
        
        # 从文本中随机选择一个词进行插入
        word_to_insert = random.choice([w for w in words if w not in self.stop_words and w not in self.punctuation])
        
        if random.random() < insert_prob:
            words.insert(insert_pos, word_to_insert)
        
        return ''.join(words)
    
    def random_deletion(self, text: str, delete_prob: float = 0.1) -> str:
        """随机删除增强"""
        if random.random() > self.aug_prob:
            return text
        
        words = jieba.lcut(text)
        if len(words) <= 3:
            return text
        
        new_words = []
        for word in words:
            if (word in self.stop_words or 
                word in self.punctuation or 
                random.random() > delete_prob):
                new_words.append(word)
        
        # 确保至少保留一些词
        if len(new_words) < len(words) * 0.5:
            return text
        
        return ''.join(new_words)
    
    def random_swap(self, text: str, swap_prob: float = 0.1) -> str:
        """随机交换增强"""
        if random.random() > self.aug_prob:
            return text
        
        words = jieba.lcut(text)
        if len(words) < 3:
            return text
        
        new_words = words.copy()
        
        for i in range(len(words)):
            if random.random() < swap_prob:
                # 随机选择另一个位置进行交换
                j = random.randint(0, len(words)-1)
                new_words[i], new_words[j] = new_words[j], new_words[i]
        
        return ''.join(new_words)
    
    def back_translation_simulation(self, text: str) -> str:
        """模拟回译增强（简化版）"""
        if random.random() > self.aug_prob:
            return text
        
        # 简化的回译模拟：随机改变一些表达方式
        replacements = {
            '非常': '很',
            '特别': '特殊',
            '重要': '关键',
            '方法': '办法',
            '问题': '难题',
            '研究': '探索',
            '分析': '解析',
            '结果': '成果',
            '影响': '作用',
            '发展': '进步'
        }
        
        for old, new in replacements.items():
            if old in text and random.random() < 0.3:
                text = text.replace(old, new)
        
        return text
    
    def augment_text(self, text: str, num_augmentations: int = 1) -> List[str]:
        """
        对文本进行多种增强
        
        Args:
            text: 原始文本
            num_augmentations: 生成的增强样本数量
        
        Returns:
            增强后的文本列表
        """
        augmented_texts = []
        
        # 定义增强方法
        augmentation_methods = [
            self.random_mask_augmentation,
            self.synonym_replacement,
            self.random_insertion,
            self.random_deletion,
            self.random_swap,
            self.back_translation_simulation
        ]
        
        for _ in range(num_augmentations):
            # 随机选择1-3种增强方法组合使用
            num_methods = random.randint(1, 3)
            selected_methods = random.sample(augmentation_methods, num_methods)
            
            augmented_text = text
            for method in selected_methods:
                augmented_text = method(augmented_text)
            
            # 确保增强后的文本不为空且与原文本不同
            if augmented_text.strip() and augmented_text != text:
                augmented_texts.append(augmented_text)
            else:
                augmented_texts.append(text)  # 如果增强失败，返回原文本
        
        return augmented_texts
    
    def augment_dataset(self, dataset: List[Dict], augment_ratio: float = 0.5) -> List[Dict]:
        """
        对整个数据集进行增强
        
        Args:
            dataset: 原始数据集
            augment_ratio: 增强比例（0-1之间）
        
        Returns:
            增强后的数据集
        """
        augmented_dataset = dataset.copy()
        
        # 计算需要增强的样本数量
        num_to_augment = int(len(dataset) * augment_ratio)
        samples_to_augment = random.sample(dataset, num_to_augment)
        
        for sample in samples_to_augment:
            # 对文本进行增强
            original_text = sample.get('text', '')
            if original_text:
                augmented_texts = self.augment_text(original_text, num_augmentations=1)
                
                for aug_text in augmented_texts:
                    # 创建新的增强样本
                    aug_sample = sample.copy()
                    aug_sample['text'] = aug_text
                    aug_sample['is_augmented'] = True
                    augmented_dataset.append(aug_sample)
        
        return augmented_dataset

def create_augmented_training_data(original_data_path: str, output_path: str, tokenizer_path: str):
    """
    创建增强训练数据
    
    Args:
        original_data_path: 原始数据路径
        output_path: 输出路径
        tokenizer_path: tokenizer路径
    """
    # 加载tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    
    # 创建数据增强器
    augmenter = AdvancedDataAugmentation(tokenizer, aug_prob=0.4)
    
    # 加载原始数据
    with open(original_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"原始数据量: {len(original_data)}")
    
    # 进行数据增强
    augmented_data = augmenter.augment_dataset(original_data, augment_ratio=0.3)
    
    print(f"增强后数据量: {len(augmented_data)}")
    
    # 保存增强数据
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(augmented_data, f, ensure_ascii=False, indent=2)
    
    print(f"增强数据已保存到: {output_path}")

if __name__ == "__main__":
    # 示例使用
    tokenizer_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large"
    original_data_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_train.json"
    output_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_train_augmented.json"
    
    create_augmented_training_data(original_data_path, output_path, tokenizer_path)
