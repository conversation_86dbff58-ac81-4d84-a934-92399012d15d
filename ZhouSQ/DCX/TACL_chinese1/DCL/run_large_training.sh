#!/bin/bash

# 高性能Large模型训练启动脚本
# 充分利用4张A100 GPU进行分布式训练

echo "🚀 启动chinese-roberta-wwm-ext-large模型高性能训练"
echo "=================================================="

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export NCCL_DEBUG=INFO
export NCCL_SOCKET_IFNAME=^docker0,lo

# 创建必要的目录
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts
mkdir -p /home/<USER>/ZhouSQ/DCX/TACL_chinese1/runs

# 检查GPU状态
echo "检查GPU状态:"
nvidia-smi

echo ""
echo "开始分布式训练..."

# 使用torch.distributed.launch启动分布式训练
python -m torch.distributed.launch \
    --nproc_per_node=4 \
    --master_port=29500 \
    /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/train_large_optimized.py \
    --model_name_or_path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext-large \
    --batch_size 16 \
    --gradient_accumulation_steps 4 \
    --lr 1e-5 \
    --lr2 3e-5 \
    --max_epochs 15 \
    --warmup_ratio 0.1 \
    --weight_decay 0.01 \
    --use_fp16 True \
    --scheduler_type cosine \
    --label_smoothing 0.1 \
    --contrastive_loss 1 \
    --contrastive_alpha 0.1 \
    --lm_alpha 0.6 \
    --eval_steps 100 \
    --save_steps 500 \
    --early_stop 5 \
    --seed 171

echo ""
echo "训练完成！"
echo "检查结果文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/large_model_results.txt"
echo "检查模型文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/"
echo "检查TensorBoard日志: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/runs/"
